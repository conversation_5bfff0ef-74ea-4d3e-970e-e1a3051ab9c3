<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethernet Cable Termination Training</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .game-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .game-card:hover {
            transform: translateY(-10px);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .game-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .game-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .game-description {
            font-size: 1rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        .standards {
            margin-top: 15px;
            font-size: 0.9rem;
            color: #ffd700;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .games-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Ethernet Cable Termination Training</h1>
        <p class="subtitle">Master the art of cable termination with interactive simulations</p>
        
        <div class="games-grid">
            <div class="game-card" onclick="window.location.href='straight-through/index.html'">
                <div class="game-icon">📡</div>
                <div class="game-title">Straight-Through Cable</div>
                <div class="game-description">
                    Learn to terminate straight-through Ethernet cables used for connecting different types of devices (PC to switch, router to switch).
                </div>
                <div class="standards">Standard: T568B</div>
            </div>
            
            <div class="game-card" onclick="window.location.href='crossover/index.html'">
                <div class="game-icon">🔀</div>
                <div class="game-title">Crossover Cable</div>
                <div class="game-description">
                    Master crossover cable termination for connecting similar devices (PC to PC, switch to switch) with different standards on each end.
                </div>
                <div class="standards">Standards: T568A ↔ T568B</div>
            </div>
        </div>
    </div>
</body>
</html>
