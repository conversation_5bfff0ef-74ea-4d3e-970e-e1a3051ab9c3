// Game State
let gameState = {
    currentStep: 1,
    score: 0,
    startTime: null,
    isGameActive: false,
    selectedTool: null,
    cableStripped: false,
    wiresVisible: false,
    wiresPlaced: [],
    gameCompleted: false,
    selectedWire: null,
    clickMode: true // Toggle between click and drag modes
};

// T568B Wire Order
const T568B_ORDER = [
    'orange-white', 'orange', 'green-white', 'blue',
    'blue-white', 'green', 'brown-white', 'brown'
];

// Wire colors for visual representation
const WIRE_COLORS = {
    'orange-white': 'linear-gradient(45deg, #ff9800, #fff)',
    'orange': '#ff9800',
    'green-white': 'linear-gradient(45deg, #4caf50, #fff)',
    'blue': '#2196f3',
    'blue-white': 'linear-gradient(45deg, #2196f3, #fff)',
    'green': '#4caf50',
    'brown-white': 'linear-gradient(45deg, #8d6e63, #fff)',
    'brown': '#8d6e63'
};

// Sound effects (using Web Audio API)
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

function playSound(frequency, duration, type = 'sine') {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = frequency;
    oscillator.type = type;
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// Initialize game
function initGame() {
    gameState.startTime = Date.now();
    gameState.isGameActive = true;
    
    createWires();
    setupEventListeners();
    updateTimer();
    updateStep(1);
    
    // Play start sound
    playSound(440, 0.2);
}

// Create wire elements
function createWires() {
    const wiresContainer = document.getElementById('cable-wires');
    wiresContainer.innerHTML = '';
    
    // Shuffle wires to make it challenging
    const shuffledColors = [...T568B_ORDER].sort(() => Math.random() - 0.5);
    
    shuffledColors.forEach((color, index) => {
        const wire = document.createElement('div');
        wire.className = 'wire';
        wire.setAttribute('data-color', color);
        wire.setAttribute('data-original-position', index);
        wire.draggable = true;
        
        // Add wire styling
        wire.style.background = WIRE_COLORS[color];
        
        wiresContainer.appendChild(wire);
    });
}

// Setup event listeners
function setupEventListeners() {
    // Tool selection
    document.querySelectorAll('.tool').forEach(tool => {
        tool.addEventListener('click', selectTool);
    });
    
    // Cable sheath stripping
    document.getElementById('cable-sheath').addEventListener('click', stripCable);
    
    // Wire drag and drop
    setupWireDragDrop();
    
    // Control buttons
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('hint-btn').addEventListener('click', showHint);
    document.getElementById('check-btn').addEventListener('click', checkWork);
    
    // RJ45 connector slots
    document.querySelectorAll('.slot').forEach(slot => {
        slot.addEventListener('dragover', handleDragOver);
        slot.addEventListener('drop', handleDrop);
        slot.addEventListener('dragenter', handleDragEnter);
        slot.addEventListener('dragleave', handleDragLeave);
    });
}

// Tool selection
function selectTool(event) {
    // Remove active class from all tools
    document.querySelectorAll('.tool').forEach(tool => {
        tool.classList.remove('active');
    });
    
    // Add active class to selected tool
    event.currentTarget.classList.add('active');
    gameState.selectedTool = event.currentTarget.getAttribute('data-tool');
    
    // Play tool selection sound
    playSound(660, 0.1);
}

// Strip cable sheath
function stripCable() {
    if (gameState.selectedTool !== 'stripper' || gameState.cableStripped) {
        if (!gameState.cableStripped) {
            showMessage('Select the wire stripper first!');
        }
        return;
    }

    const cableSheath = document.getElementById('cable-sheath');
    const cableWires = document.getElementById('cable-wires');

    // Add cutting animation
    cableSheath.classList.add('cutting');

    // Play cutting sound sequence
    playSound(800, 0.3, 'sawtooth');
    setTimeout(() => playSound(600, 0.2, 'square'), 500);
    setTimeout(() => playSound(400, 0.2, 'triangle'), 1000);
    setTimeout(() => playSound(300, 0.3, 'sawtooth'), 1500);

    // After animation completes, show wires
    setTimeout(() => {
        cableSheath.classList.add('stripped');
        cableSheath.classList.remove('cutting');
        cableWires.classList.add('visible');

        gameState.cableStripped = true;
        gameState.wiresVisible = true;
        gameState.score += 100;

        updateScore();
        updateStep(2);

        // Success sound
        playSound(880, 0.2, 'sine');
    }, 2000);
}

// Wire interaction setup (both click and drag)
function setupWireDragDrop() {
    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('dragend', handleDragEnd);
    document.addEventListener('click', handleWireClick);
}

function handleDragStart(event) {
    if (!event.target.classList.contains('wire')) return;
    
    event.target.classList.add('dragging');
    event.dataTransfer.setData('text/plain', event.target.getAttribute('data-color'));
    event.dataTransfer.effectAllowed = 'move';
}

function handleDragEnd(event) {
    if (!event.target.classList.contains('wire')) return;

    event.target.classList.remove('dragging');
}

// Handle wire click for click-to-select mode
function handleWireClick(event) {
    if (!event.target.classList.contains('wire')) {
        // If clicking on a slot and we have a selected wire
        if (event.target.classList.contains('slot') && gameState.selectedWire) {
            handleSlotClick(event);
        }
        return;
    }

    // Clear previous selection
    document.querySelectorAll('.wire').forEach(wire => {
        wire.classList.remove('selected');
    });

    // Select the clicked wire
    event.target.classList.add('selected');
    gameState.selectedWire = event.target.getAttribute('data-color');

    // Play selection sound
    playSound(440, 0.1);
}

// Handle slot click when wire is selected
function handleSlotClick(event) {
    if (!gameState.selectedWire) return;

    const slotPosition = parseInt(event.target.getAttribute('data-position'));

    // Check if slot is already filled
    if (event.target.classList.contains('filled')) {
        showMessage('This slot is already filled!');
        return;
    }

    // Place wire in slot
    event.target.classList.add('filled');
    event.target.style.setProperty('--wire-color', WIRE_COLORS[gameState.selectedWire]);

    // Remove wire from cable area
    const wireElement = document.querySelector(`[data-color="${gameState.selectedWire}"]`);
    if (wireElement) {
        wireElement.style.display = 'none';
    }

    // Update game state
    gameState.wiresPlaced[slotPosition - 1] = gameState.selectedWire;

    // Clear selection
    gameState.selectedWire = null;
    document.querySelectorAll('.wire').forEach(wire => {
        wire.classList.remove('selected');
    });

    // Play placement sound
    playSound(440 + (slotPosition * 50), 0.1);

    // Check if all wires are placed
    if (gameState.wiresPlaced.filter(wire => wire).length === 8) {
        updateStep(4);
    } else if (gameState.wiresPlaced.filter(wire => wire).length === 1) {
        updateStep(3);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
}

function handleDragEnter(event) {
    if (event.target.classList.contains('slot')) {
        event.target.classList.add('highlight');
    }
}

function handleDragLeave(event) {
    if (event.target.classList.contains('slot')) {
        event.target.classList.remove('highlight');
    }
}

function handleDrop(event) {
    event.preventDefault();
    
    if (!event.target.classList.contains('slot')) return;
    
    const wireColor = event.dataTransfer.getData('text/plain');
    const slotPosition = parseInt(event.target.getAttribute('data-position'));
    
    // Remove highlight
    event.target.classList.remove('highlight');
    
    // Check if slot is already filled
    if (event.target.classList.contains('filled')) {
        showMessage('This slot is already filled!');
        return;
    }
    
    // Place wire in slot
    event.target.classList.add('filled');
    event.target.style.setProperty('--wire-color', WIRE_COLORS[wireColor]);
    
    // Remove wire from cable area
    const wireElement = document.querySelector(`[data-color="${wireColor}"]`);
    if (wireElement) {
        wireElement.style.display = 'none';
    }
    
    // Update game state
    gameState.wiresPlaced[slotPosition - 1] = wireColor;
    
    // Play placement sound
    playSound(440 + (slotPosition * 50), 0.1);
    
    // Check if all wires are placed
    if (gameState.wiresPlaced.filter(wire => wire).length === 8) {
        updateStep(4);
    } else if (gameState.wiresPlaced.filter(wire => wire).length === 1) {
        updateStep(3);
    }
}

// Check work
function checkWork() {
    if (gameState.wiresPlaced.filter(wire => wire).length !== 8) {
        showMessage('Please place all wires in the connector first!');
        return;
    }

    let correctPlacements = 0;
    let errors = [];

    for (let i = 0; i < 8; i++) {
        if (gameState.wiresPlaced[i] === T568B_ORDER[i]) {
            correctPlacements++;
        } else {
            errors.push({
                position: i + 1,
                expected: T568B_ORDER[i],
                actual: gameState.wiresPlaced[i]
            });
        }
    }

    if (correctPlacements === 8) {
        // Perfect score! Start crimping animation
        const connectorBody = document.querySelector('.connector-body');
        connectorBody.classList.add('crimping');

        // Play crimping sound sequence
        playSound(300, 0.3, 'square');
        setTimeout(() => playSound(250, 0.3, 'square'), 400);
        setTimeout(() => playSound(200, 0.4, 'square'), 800);

        // After crimping animation
        setTimeout(() => {
            gameState.score += 500;
            gameState.gameCompleted = true;
            updateScore();
            updateStep(4);

            // Play success sound sequence
            playSound(523, 0.2); // C note
            setTimeout(() => playSound(659, 0.2), 200); // E note
            setTimeout(() => playSound(784, 0.4), 400); // G note

            showSuccessModal();
        }, 2000);
    } else {
        // Show errors
        let errorMessage = `${correctPlacements}/8 wires correct.\n\nErrors:\n`;
        errors.forEach(error => {
            errorMessage += `Position ${error.position}: Expected ${error.expected}, got ${error.actual}\n`;
        });

        showMessage(errorMessage);

        // Deduct points for errors
        gameState.score = Math.max(0, gameState.score - (errors.length * 25));
        updateScore();

        // Play error sound
        playSound(200, 0.5, 'sawtooth');
    }
}

// Update score display
function updateScore() {
    document.getElementById('score').textContent = gameState.score;
}

// Update timer
function updateTimer() {
    if (!gameState.isGameActive || gameState.gameCompleted) return;
    
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    document.getElementById('timer').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    setTimeout(updateTimer, 1000);
}

// Update current step
function updateStep(step) {
    const stepTexts = {
        1: "Step 1: Strip the cable sheath",
        2: "Step 2: Arrange wires in T568B order",
        3: "Step 3: Insert wires into RJ45 connector",
        4: "Step 4: Crimp the connector",
        5: "Step 5: Test your work"
    };

    const currentStepEl = document.getElementById('current-step');
    if (currentStepEl && stepTexts[step]) {
        currentStepEl.textContent = stepTexts[step];
    }

    gameState.currentStep = step;
}

// Show hint
function showHint() {
    const hints = {
        1: "Click on the wire stripper tool, then click on the cable to strip the sheath.",
        2: "Drag the wires from the cable to arrange them in the correct T568B order shown in the reference.",
        3: "Drag each wire to the correct position in the RJ45 connector slots.",
        4: "Click 'Check Work' to verify your wire placement and complete the termination."
    };

    const hintText = hints[gameState.currentStep] || "Follow the current step instructions.";
    document.getElementById('hint-text').textContent = hintText;
    showModal('hint-modal');

    // Play hint sound
    playSound(880, 0.1);
}

// Show message
function showMessage(message) {
    alert(message); // Simple alert for now, could be enhanced with custom modal
}

// Show success modal
function showSuccessModal() {
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('final-score').textContent = gameState.score;
    document.getElementById('final-time').textContent = timeString;
    
    showModal('success-modal');
}

// Modal functions
function showModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Reset game
function resetGame() {
    gameState = {
        currentStep: 1,
        score: 0,
        startTime: null,
        isGameActive: false,
        selectedTool: null,
        cableStripped: false,
        wiresVisible: false,
        wiresPlaced: [],
        gameCompleted: false,
        selectedWire: null,
        clickMode: true
    };

    // Reset UI
    document.getElementById('cable-sheath').classList.remove('stripped');
    document.getElementById('cable-wires').classList.remove('visible');

    document.querySelectorAll('.slot').forEach(slot => {
        slot.classList.remove('filled');
        slot.style.removeProperty('--wire-color');
    });

    document.querySelectorAll('.tool').forEach(tool => {
        tool.classList.remove('active');
    });

    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });

    updateScore();
    initGame();
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', initGame);
