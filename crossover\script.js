// Game State
let gameState = {
    currentStep: 1,
    score: 0,
    startTime: null,
    isGameActive: false,
    selectedTool: null,
    cableStrippedA: false,
    cableStrippedB: false,
    wiresVisibleA: false,
    wiresVisibleB: false,
    wiresPlacedA: [],
    wiresPlacedB: [],
    gameCompleted: false,
    selectedWire: null,
    selectedWireEnd: null,
    clickMode: true
};

// T568A and T568B Wire Orders
const T568A_ORDER = [
    'green-white', 'green', 'orange-white', 'blue',
    'blue-white', 'orange', 'brown-white', 'brown'
];

const T568B_ORDER = [
    'orange-white', 'orange', 'green-white', 'blue',
    'blue-white', 'green', 'brown-white', 'brown'
];

// Wire colors for visual representation
const WIRE_COLORS = {
    'orange-white': 'linear-gradient(45deg, #ff9800, #fff)',
    'orange': '#ff9800',
    'green-white': 'linear-gradient(45deg, #4caf50, #fff)',
    'blue': '#2196f3',
    'blue-white': 'linear-gradient(45deg, #2196f3, #fff)',
    'green': '#4caf50',
    'brown-white': 'linear-gradient(45deg, #8d6e63, #fff)',
    'brown': '#8d6e63'
};

// Sound effects (using Web Audio API)
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

function playSound(frequency, duration, type = 'sine') {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = frequency;
    oscillator.type = type;
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// Initialize game
function initGame() {
    gameState.startTime = Date.now();
    gameState.isGameActive = true;
    
    createWires();
    setupEventListeners();
    updateTimer();
    updateStep(1);
    
    // Play start sound
    playSound(440, 0.2);
}

// Create wire elements for both ends
function createWires() {
    const wiresContainerA = document.getElementById('cable-wires-a');
    const wiresContainerB = document.getElementById('cable-wires-b');
    
    wiresContainerA.innerHTML = '';
    wiresContainerB.innerHTML = '';
    
    // Create identical sets of wires for both ends
    const allColors = [...T568A_ORDER];
    
    // Shuffle wires to make it challenging
    const shuffledColorsA = [...allColors].sort(() => Math.random() - 0.5);
    const shuffledColorsB = [...allColors].sort(() => Math.random() - 0.5);
    
    // Create wires for End A
    shuffledColorsA.forEach((color, index) => {
        const wire = document.createElement('div');
        wire.className = 'wire';
        wire.setAttribute('data-color', color);
        wire.setAttribute('data-end', 'a');
        wire.setAttribute('data-original-position', index);
        wire.draggable = true;
        wire.style.background = WIRE_COLORS[color];
        wiresContainerA.appendChild(wire);
    });
    
    // Create wires for End B
    shuffledColorsB.forEach((color, index) => {
        const wire = document.createElement('div');
        wire.className = 'wire';
        wire.setAttribute('data-color', color);
        wire.setAttribute('data-end', 'b');
        wire.setAttribute('data-original-position', index);
        wire.draggable = true;
        wire.style.background = WIRE_COLORS[color];
        wiresContainerB.appendChild(wire);
    });
}

// Setup event listeners
function setupEventListeners() {
    // Tool selection
    document.querySelectorAll('.tool').forEach(tool => {
        tool.addEventListener('click', selectTool);
    });
    
    // Cable sheath stripping
    document.getElementById('cable-sheath-a').addEventListener('click', () => stripCable('a'));
    document.getElementById('cable-sheath-b').addEventListener('click', () => stripCable('b'));
    
    // Wire drag and drop
    setupWireDragDrop();
    
    // Control buttons
    document.getElementById('reset-btn').addEventListener('click', resetGame);
    document.getElementById('hint-btn').addEventListener('click', showHint);
    document.getElementById('check-btn').addEventListener('click', checkWork);
    
    // RJ45 connector slots
    document.querySelectorAll('.slot').forEach(slot => {
        slot.addEventListener('dragover', handleDragOver);
        slot.addEventListener('drop', handleDrop);
        slot.addEventListener('dragenter', handleDragEnter);
        slot.addEventListener('dragleave', handleDragLeave);
    });
}

// Tool selection
function selectTool(event) {
    document.querySelectorAll('.tool').forEach(tool => {
        tool.classList.remove('active');
    });
    
    event.currentTarget.classList.add('active');
    gameState.selectedTool = event.currentTarget.getAttribute('data-tool');
    
    playSound(660, 0.1);
}

// Strip cable sheath
function stripCable(end) {
    if (gameState.selectedTool !== 'stripper') {
        showMessage('Select the wire stripper first!');
        return;
    }

    const cableSheath = document.getElementById(`cable-sheath-${end}`);
    const cableWires = document.getElementById(`cable-wires-${end}`);

    if (end === 'a' && gameState.cableStrippedA) return;
    if (end === 'b' && gameState.cableStrippedB) return;

    // Add cutting animation
    cableSheath.classList.add('cutting');

    // Play cutting sound sequence
    playSound(800, 0.3, 'sawtooth');
    setTimeout(() => playSound(600, 0.2, 'square'), 500);
    setTimeout(() => playSound(400, 0.2, 'triangle'), 1000);
    setTimeout(() => playSound(300, 0.3, 'sawtooth'), 1500);

    // After animation completes, show wires
    setTimeout(() => {
        cableSheath.classList.add('stripped');
        cableSheath.classList.remove('cutting');
        cableWires.classList.add('visible');

        if (end === 'a') {
            gameState.cableStrippedA = true;
            gameState.wiresVisibleA = true;
        } else {
            gameState.cableStrippedB = true;
            gameState.wiresVisibleB = true;
        }

        gameState.score += 100;
        updateScore();

        // Check if both ends are stripped
        if (gameState.cableStrippedA && gameState.cableStrippedB) {
            updateStep(2);
        }

        // Success sound
        playSound(880, 0.2, 'sine');
    }, 2000);
}

// Wire interaction setup (both click and drag)
function setupWireDragDrop() {
    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('dragend', handleDragEnd);
    document.addEventListener('click', handleWireClick);
}

function handleDragStart(event) {
    if (!event.target.classList.contains('wire')) return;
    
    event.target.classList.add('dragging');
    const wireData = {
        color: event.target.getAttribute('data-color'),
        end: event.target.getAttribute('data-end')
    };
    event.dataTransfer.setData('text/plain', JSON.stringify(wireData));
    event.dataTransfer.effectAllowed = 'move';
}

function handleDragEnd(event) {
    if (!event.target.classList.contains('wire')) return;
    event.target.classList.remove('dragging');
}

// Handle wire click for click-to-select mode
function handleWireClick(event) {
    if (!event.target.classList.contains('wire')) {
        // If clicking on a slot and we have a selected wire
        if (event.target.classList.contains('slot') && gameState.selectedWire) {
            handleSlotClick(event);
        }
        return;
    }

    // Clear previous selection
    document.querySelectorAll('.wire').forEach(wire => {
        wire.classList.remove('selected');
    });

    // Select the clicked wire
    event.target.classList.add('selected');
    gameState.selectedWire = event.target.getAttribute('data-color');
    gameState.selectedWireEnd = event.target.getAttribute('data-end');

    // Play selection sound
    playSound(440, 0.1);
}

// Handle slot click when wire is selected
function handleSlotClick(event) {
    if (!gameState.selectedWire) return;

    const slotPosition = parseInt(event.target.getAttribute('data-position'));
    const slotEnd = event.target.getAttribute('data-end');

    // Check if wire and slot match the same end
    if (gameState.selectedWireEnd !== slotEnd) {
        showMessage('Wire must be placed in the connector for the same end!');
        return;
    }

    // Check if slot is already filled
    if (event.target.classList.contains('filled')) {
        showMessage('This slot is already filled!');
        return;
    }

    // Place wire in slot
    event.target.classList.add('filled');
    event.target.style.setProperty('--wire-color', WIRE_COLORS[gameState.selectedWire]);

    // Remove wire from cable area
    const wireElement = document.querySelector(`[data-color="${gameState.selectedWire}"][data-end="${gameState.selectedWireEnd}"]`);
    if (wireElement) {
        wireElement.style.display = 'none';
    }

    // Update game state
    if (slotEnd === 'a') {
        gameState.wiresPlacedA[slotPosition - 1] = gameState.selectedWire;
    } else {
        gameState.wiresPlacedB[slotPosition - 1] = gameState.selectedWire;
    }

    // Clear selection
    gameState.selectedWire = null;
    gameState.selectedWireEnd = null;
    document.querySelectorAll('.wire').forEach(wire => {
        wire.classList.remove('selected');
    });

    // Play placement sound
    playSound(440 + (slotPosition * 50), 0.1);

    // Check progress
    const totalWiresPlaced = gameState.wiresPlacedA.filter(w => w).length +
                            gameState.wiresPlacedB.filter(w => w).length;

    if (totalWiresPlaced === 16) {
        updateStep(4);
    } else if (totalWiresPlaced >= 8 && gameState.currentStep < 3) {
        updateStep(3);
    } else if (totalWiresPlaced >= 1 && gameState.currentStep < 3) {
        updateStep(3);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
}

function handleDragEnter(event) {
    if (event.target.classList.contains('slot')) {
        event.target.classList.add('highlight');
    }
}

function handleDragLeave(event) {
    if (event.target.classList.contains('slot')) {
        event.target.classList.remove('highlight');
    }
}

function handleDrop(event) {
    event.preventDefault();
    
    if (!event.target.classList.contains('slot')) return;
    
    const wireData = JSON.parse(event.dataTransfer.getData('text/plain'));
    const slotPosition = parseInt(event.target.getAttribute('data-position'));
    const slotEnd = event.target.getAttribute('data-end');
    
    // Remove highlight
    event.target.classList.remove('highlight');
    
    // Check if wire and slot match the same end
    if (wireData.end !== slotEnd) {
        showMessage('Wire must be placed in the connector for the same end!');
        return;
    }
    
    // Check if slot is already filled
    if (event.target.classList.contains('filled')) {
        showMessage('This slot is already filled!');
        return;
    }
    
    // Place wire in slot
    event.target.classList.add('filled');
    event.target.style.setProperty('--wire-color', WIRE_COLORS[wireData.color]);
    
    // Remove wire from cable area
    const wireElement = document.querySelector(`[data-color="${wireData.color}"][data-end="${wireData.end}"]`);
    if (wireElement) {
        wireElement.style.display = 'none';
    }
    
    // Update game state
    if (slotEnd === 'a') {
        gameState.wiresPlacedA[slotPosition - 1] = wireData.color;
    } else {
        gameState.wiresPlacedB[slotPosition - 1] = wireData.color;
    }
    
    playSound(440 + (slotPosition * 50), 0.1);
    
    // Check progress
    const totalWiresPlaced = gameState.wiresPlacedA.filter(w => w).length +
                            gameState.wiresPlacedB.filter(w => w).length;

    if (totalWiresPlaced === 16) {
        updateStep(4);
    } else if (totalWiresPlaced >= 8 && gameState.currentStep < 3) {
        updateStep(3);
    } else if (totalWiresPlaced >= 1 && gameState.currentStep < 3) {
        updateStep(3);
    }
}



// Check work
function checkWork() {
    const wiresPlacedA = gameState.wiresPlacedA.filter(wire => wire).length;
    const wiresPlacedB = gameState.wiresPlacedB.filter(wire => wire).length;
    
    if (wiresPlacedA !== 8 || wiresPlacedB !== 8) {
        showMessage('Please place all wires in both connectors first!');
        return;
    }
    
    let correctPlacementsA = 0;
    let correctPlacementsB = 0;
    let errorsA = [];
    let errorsB = [];
    
    // Check End A (T568A)
    for (let i = 0; i < 8; i++) {
        if (gameState.wiresPlacedA[i] === T568A_ORDER[i]) {
            correctPlacementsA++;
        } else {
            errorsA.push({
                position: i + 1,
                expected: T568A_ORDER[i],
                actual: gameState.wiresPlacedA[i]
            });
        }
    }
    
    // Check End B (T568B)
    for (let i = 0; i < 8; i++) {
        if (gameState.wiresPlacedB[i] === T568B_ORDER[i]) {
            correctPlacementsB++;
        } else {
            errorsB.push({
                position: i + 1,
                expected: T568B_ORDER[i],
                actual: gameState.wiresPlacedB[i]
            });
        }
    }
    
    if (correctPlacementsA === 8 && correctPlacementsB === 8) {
        // Perfect crossover cable! Start crimping animation for both connectors
        const connectorBodyA = document.querySelector('#rj45-connector-a .connector-body');
        const connectorBodyB = document.querySelector('#rj45-connector-b .connector-body');

        connectorBodyA.classList.add('crimping');
        setTimeout(() => connectorBodyB.classList.add('crimping'), 500);

        // Play crimping sound sequence for both connectors
        playSound(300, 0.3, 'square');
        setTimeout(() => playSound(250, 0.3, 'square'), 400);
        setTimeout(() => playSound(280, 0.3, 'square'), 900);
        setTimeout(() => playSound(220, 0.4, 'square'), 1300);

        // After crimping animation
        setTimeout(() => {
            gameState.score += 1000;
            gameState.gameCompleted = true;
            updateScore();

            // Play success sound sequence
            playSound(523, 0.2); // C note
            setTimeout(() => playSound(659, 0.2), 200); // E note
            setTimeout(() => playSound(784, 0.2), 400); // G note
            setTimeout(() => playSound(1047, 0.4), 600); // High C note

            showSuccessModal();
        }, 2500);
    } else {
        // Show errors
        let errorMessage = `End A (T568A): ${correctPlacementsA}/8 correct\n`;
        errorMessage += `End B (T568B): ${correctPlacementsB}/8 correct\n\n`;
        
        if (errorsA.length > 0) {
            errorMessage += 'End A Errors:\n';
            errorsA.forEach(error => {
                errorMessage += `Position ${error.position}: Expected ${error.expected}, got ${error.actual}\n`;
            });
        }
        
        if (errorsB.length > 0) {
            errorMessage += '\nEnd B Errors:\n';
            errorsB.forEach(error => {
                errorMessage += `Position ${error.position}: Expected ${error.expected}, got ${error.actual}\n`;
            });
        }
        
        showMessage(errorMessage);
        
        // Deduct points for errors
        const totalErrors = errorsA.length + errorsB.length;
        gameState.score = Math.max(0, gameState.score - (totalErrors * 50));
        updateScore();
        
        playSound(200, 0.5, 'sawtooth');
    }
}

// Update score display
function updateScore() {
    document.getElementById('score').textContent = gameState.score;
}

// Update timer
function updateTimer() {
    if (!gameState.isGameActive || gameState.gameCompleted) return;
    
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    document.getElementById('timer').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    setTimeout(updateTimer, 1000);
}

// Update current step
function updateStep(step) {
    const stepTexts = {
        1: "Step 1: Strip both cable ends",
        2: "Step 2: Arrange End A wires in T568A order",
        3: "Step 3: Arrange End B wires in T568B order",
        4: "Step 4: Check your crossover cable termination"
    };

    const currentStepEl = document.getElementById('current-step');
    if (currentStepEl && stepTexts[step]) {
        currentStepEl.textContent = stepTexts[step];
    }

    gameState.currentStep = step;
}

// Show hint
function showHint() {
    const hints = {
        1: "Click on the wire stripper tool, then click on both cable ends to strip the sheaths.",
        2: "Drag wires from End A to arrange them in T568A order according to the reference guide.",
        3: "Drag wires from End B to arrange them in T568B order according to the reference guide.",
        4: "Click 'Check Work' to verify your crossover cable termination."
    };

    const hintText = hints[gameState.currentStep] || "Follow the current step instructions.";
    document.getElementById('hint-text').textContent = hintText;
    showModal('hint-modal');

    playSound(880, 0.1);
}

// Show message
function showMessage(message) {
    alert(message);
}

// Show success modal
function showSuccessModal() {
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('final-score').textContent = gameState.score;
    document.getElementById('final-time').textContent = timeString;
    
    showModal('success-modal');
}

// Modal functions
function showModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Reset game
function resetGame() {
    gameState = {
        currentStep: 1,
        score: 0,
        startTime: null,
        isGameActive: false,
        selectedTool: null,
        cableStrippedA: false,
        cableStrippedB: false,
        wiresVisibleA: false,
        wiresVisibleB: false,
        wiresPlacedA: [],
        wiresPlacedB: [],
        gameCompleted: false,
        selectedWire: null,
        selectedWireEnd: null,
        clickMode: true
    };

    // Reset UI
    document.querySelectorAll('.cable-sheath').forEach(sheath => {
        sheath.classList.remove('stripped');
    });

    document.querySelectorAll('.cable-wires').forEach(wires => {
        wires.classList.remove('visible');
    });

    document.querySelectorAll('.slot').forEach(slot => {
        slot.classList.remove('filled');
        slot.style.removeProperty('--wire-color');
    });

    document.querySelectorAll('.tool').forEach(tool => {
        tool.classList.remove('active');
    });

    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });

    updateScore();
    initGame();
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', initGame);
