/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.game-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    padding-bottom: 80px; /* Space for fixed footer */
}

/* Header Styles */
.game-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.back-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #5a6268;
    transform: translateX(-2px);
}

.game-header h1 {
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 600;
}

.header-right {
    display: flex;
    gap: 30px;
    align-items: center;
}

.score-display, .timer-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.score-label, .timer-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.score-value, .timer-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Main Game Area */
.game-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
}

/* Tools Section */
.tools-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.tools-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4rem;
    text-align: center;
}

.tools-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.tool {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    border: 3px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tool:hover {
    border-color: #2196f3;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.tool.active {
    border-color: #2196f3;
    background: #e3f2fd;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.tool-icon {
    font-size: 2.5rem;
}

.tool span {
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    color: #2c3e50;
}

.tool-description {
    font-size: 0.8rem;
    color: #6c757d;
    text-align: center;
    font-style: italic;
}

/* Cable Workspace */
.cable-workspace {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 25px; /* Removed extra bottom padding since footer is now fixed */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 20px; /* Space above fixed footer */
}

.workspace-header {
    text-align: center;
    margin-bottom: 30px;
}

.workspace-header h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.current-step {
    background: #e3f2fd;
    color: #1565c0;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    border: 2px solid #2196f3;
    display: inline-block;
}

.cable-work-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
    min-height: 400px; /* Ensure minimum height */
    padding-bottom: 20px; /* Extra space at bottom */
}

.cable-section, .connector-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.cable-section h3, .connector-section h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin-bottom: 10px;
    text-align: center;
}

/* Wire Order Reference */
.wire-order-reference {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    width: 100%;
}

.wire-order-reference h4 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1rem;
    text-align: center;
}

.wire-reference {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.wire-ref {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    border-left: 4px solid;
}

.wire-ref[data-color="orange-white"] {
    background: #fff3e0;
    border-color: #ff9800;
    color: #e65100;
}

.wire-ref[data-color="orange"] {
    background: #ff9800;
    border-color: #e65100;
    color: white;
}

.wire-ref[data-color="green-white"] {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

.wire-ref[data-color="blue"] {
    background: #2196f3;
    border-color: #1565c0;
    color: white;
}

.wire-ref[data-color="blue-white"] {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1565c0;
}

.wire-ref[data-color="green"] {
    background: #4caf50;
    border-color: #2e7d32;
    color: white;
}

.wire-ref[data-color="brown-white"] {
    background: #efebe9;
    border-color: #8d6e63;
    color: #5d4037;
}

.wire-ref[data-color="brown"] {
    background: #8d6e63;
    border-color: #5d4037;
    color: white;
}



/* Ethernet Cable */
.ethernet-cable {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    padding: 30px 30px 40px 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 3px solid #dee2e6;
    min-height: 280px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.cable-sheath {
    width: 320px;
    height: 35px;
    background: linear-gradient(45deg, #495057 0%, #6c757d 50%, #495057 100%);
    border-radius: 18px;
    position: relative;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.1),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid #343a40;
    overflow: hidden;
}

/* Cable texture details */
.cable-sheath::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent 0px,
        rgba(255, 255, 255, 0.05) 2px,
        transparent 4px
    );
    border-radius: inherit;
}

/* Cable brand marking */
.cable-sheath::after {
    content: 'CAT6 UTP';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.6);
    font-size: 10px;
    font-weight: bold;
    letter-spacing: 1px;
}

.cable-sheath:hover {
    transform: scale(1.03);
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.3);
}

/* Wire cutting animation */
.cable-sheath.cutting {
    animation: cableCut 2.5s ease-in-out forwards;
    position: relative;
    overflow: visible;
}

.cable-sheath.cutting::before {
    content: '✂️';
    position: absolute;
    top: -25px;
    right: -15px;
    font-size: 28px;
    animation: cutterMove 2.5s ease-in-out forwards;
    z-index: 10;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

/* Cutting sparks effect */
.cable-sheath.cutting::after {
    content: '✨';
    position: absolute;
    top: -8px;
    right: -5px;
    font-size: 18px;
    animation: sparksEffect 2.5s ease-in-out forwards;
    z-index: 9;
    opacity: 0;
}

@keyframes cableCut {
    0% {
        width: 320px;
        background: linear-gradient(45deg, #495057 0%, #6c757d 50%, #495057 100%);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }
    40% {
        width: 320px;
        background: linear-gradient(45deg, #495057 0%, #6c757d 50%, #495057 100%);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    }
    60% {
        width: 320px;
        background: linear-gradient(45deg, #495057 0%, #6c757d 50%, #ff6b35 95%, #ffc107 100%);
        box-shadow: 0 10px 30px rgba(255, 193, 7, 0.6);
    }
    80% {
        width: 260px;
        background: linear-gradient(45deg, #adb5bd 0%, #6c757d 50%, #adb5bd 100%);
        border-color: #6c757d;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }
    100% {
        width: 260px;
        background: linear-gradient(45deg, #adb5bd 0%, #6c757d 50%, #adb5bd 100%);
        border-color: #6c757d;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }
}

@keyframes cutterMove {
    0% {
        right: -15px;
        top: -25px;
        transform: rotate(0deg) scale(1);
        opacity: 0;
    }
    20% {
        right: -15px;
        top: -25px;
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
    40% {
        right: -15px;
        top: -20px;
        transform: rotate(-15deg) scale(1.1);
        opacity: 1;
    }
    60% {
        right: -15px;
        top: -15px;
        transform: rotate(-45deg) scale(1.2);
        opacity: 1;
    }
    70% {
        right: -15px;
        top: -10px;
        transform: rotate(-60deg) scale(1.15);
        opacity: 1;
    }
    80% {
        right: -15px;
        top: -15px;
        transform: rotate(-30deg) scale(1.05);
        opacity: 1;
    }
    90% {
        right: -15px;
        top: -20px;
        transform: rotate(-10deg) scale(1);
        opacity: 0.5;
    }
    100% {
        right: -15px;
        top: -25px;
        transform: rotate(0deg) scale(0.8);
        opacity: 0;
    }
}

@keyframes sparksEffect {
    0% {
        opacity: 0;
        right: -5px;
        transform: scale(0.5);
    }
    40% {
        opacity: 0;
        right: -5px;
        transform: scale(0.5);
    }
    60% {
        opacity: 1;
        right: -5px;
        transform: scale(1.2) rotate(45deg);
    }
    70% {
        opacity: 0.8;
        right: -3px;
        transform: scale(1.4) rotate(90deg);
    }
    80% {
        opacity: 0.6;
        right: -1px;
        transform: scale(1.2) rotate(135deg);
    }
    90% {
        opacity: 0.3;
        right: 1px;
        transform: scale(1) rotate(180deg);
    }
    100% {
        opacity: 0;
        right: 3px;
        transform: scale(0.6) rotate(225deg);
    }
}

.cable-sheath.stripped {
    width: 260px;
    background: linear-gradient(45deg, #adb5bd 0%, #6c757d 50%, #adb5bd 100%);
    border-color: #6c757d;
}

/* Wire ends visible after cutting - initially hidden */
.cable-sheath.stripped::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 20px;
    background:
        linear-gradient(90deg,
            #ff9800 0%, #ff9800 12.5%,
            #4caf50 12.5%, #4caf50 25%,
            #2196f3 25%, #2196f3 37.5%,
            #8d6e63 37.5%, #8d6e63 50%,
            #ff9800 50%, #ff9800 62.5%,
            #4caf50 62.5%, #4caf50 75%,
            #2196f3 75%, #2196f3 87.5%,
            #8d6e63 87.5%, #8d6e63 100%
        );
    border-radius: 0 8px 8px 0;
    border: 1px solid #495057;
    border-left: none;
    box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
    opacity: 0;
    animation: wireEndsReveal 0.5s ease-out 2.5s forwards;
}

@keyframes wireEndsReveal {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

.cable-wires {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    opacity: 0;
    transition: all 0.5s ease;
    padding: 15px 15px 25px 15px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    min-height: 100px;
    width: 100%;
}

.cable-wires.visible {
    opacity: 1;
}

.wire {
    width: 10px;
    height: 90px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(0, 0, 0, 0.2);
    animation: wireAppear 0.6s ease-out both;
}

.wire.selected {
    border-color: #2196f3;
    box-shadow:
        0 0 15px rgba(33, 150, 243, 0.6),
        0 3px 8px rgba(0, 0, 0, 0.3);
    transform: scale(1.1);
}

@keyframes wireAppear {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Stagger wire animations */
.wire:nth-child(1) { animation-delay: 0.1s; }
.wire:nth-child(2) { animation-delay: 0.2s; }
.wire:nth-child(3) { animation-delay: 0.3s; }
.wire:nth-child(4) { animation-delay: 0.4s; }
.wire:nth-child(5) { animation-delay: 0.5s; }
.wire:nth-child(6) { animation-delay: 0.6s; }
.wire:nth-child(7) { animation-delay: 0.7s; }
.wire:nth-child(8) { animation-delay: 0.8s; }

/* Wire insulation texture */
.wire::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: repeating-linear-gradient(
        0deg,
        transparent 0px,
        rgba(255, 255, 255, 0.1) 1px,
        transparent 2px
    );
    pointer-events: none;
}

/* Wire copper core - visible at the tip */
.wire::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #cd7f32;
    border-radius: 50% 50% 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.wire:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.wire.dragging {
    cursor: grabbing;
    transform: scale(1.2);
    z-index: 100;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
}

.wire.trimmed {
    height: 55px;
}

/* Wire Colors */
.wire[data-color="orange-white"] {
    background: linear-gradient(45deg, #ff9800, #fff);
}

.wire[data-color="orange"] {
    background: #ff9800;
}

.wire[data-color="green-white"] {
    background: linear-gradient(45deg, #4caf50, #fff);
}

.wire[data-color="blue"] {
    background: #2196f3;
}

.wire[data-color="blue-white"] {
    background: linear-gradient(45deg, #2196f3, #fff);
}

.wire[data-color="green"] {
    background: #4caf50;
}

.wire[data-color="brown-white"] {
    background: linear-gradient(45deg, #8d6e63, #fff);
}

.wire[data-color="brown"] {
    background: #8d6e63;
}

/* RJ45 Connector */
.rj45-connector {
    perspective: 1500px;
    display: flex;
    justify-content: center;
    padding: 30px;
}

.connector-body {
    width: 200px;
    height: 120px;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    border-radius: 16px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.8),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
    transform: none;
    position: relative;
    border: 4px solid #adb5bd;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Connector details */
.connector-body::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 6px;
    background: linear-gradient(90deg, #6c757d, #495057, #6c757d);
    border-radius: 3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Connector brand marking */
.connector-body::after {
    content: 'RJ45';
    position: absolute;
    bottom: -6px;
    right: 8px;
    font-size: 8px;
    font-weight: bold;
    color: #6c757d;
    letter-spacing: 0.5px;
}

/* Crimping animation */
.connector-body.crimping {
    animation: connectorCrimp 2.5s ease-in-out forwards;
    position: relative;
}

.connector-body.crimping::before {
    content: '🔧';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    animation: crimperMove 2.5s ease-in-out forwards;
    z-index: 10;
}

@keyframes connectorCrimp {
    0% {
        transform: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    20% {
        transform: none;
        box-shadow: 0 10px 30px rgba(255, 193, 7, 0.4);
    }
    40% {
        transform: scaleY(0.95);
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.6);
    }
    60% {
        transform: scaleY(0.92);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }
    80% {
        transform: scaleY(0.95);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
    }
    100% {
        transform: none;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
        border-color: #28a745;
    }
}

@keyframes crimperMove {
    0% {
        top: -25px;
        transform: translateX(-50%) rotate(0deg);
    }
    20% {
        top: -20px;
        transform: translateX(-50%) rotate(-10deg);
    }
    40% {
        top: -15px;
        transform: translateX(-50%) rotate(-20deg);
    }
    60% {
        top: -10px;
        transform: translateX(-50%) rotate(-25deg);
    }
    80% {
        top: -15px;
        transform: translateX(-50%) rotate(-15deg);
    }
    100% {
        top: -25px;
        transform: translateX(-50%) rotate(0deg);
        opacity: 0;
    }
}

.connector-slots {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 18px 12px;
    height: 100%;
}

.slot {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    border-radius: 4px;
    border: 2px solid #1a252f;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 70px;
    cursor: pointer;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 -1px 2px rgba(255, 255, 255, 0.1);
}

/* Slot metal contacts */
.slot::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 1px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.slot:hover {
    background: linear-gradient(180deg, #34495e 0%, #3d566e 50%, #34495e 100%);
    border-color: #2c3e50;
    transform: translateY(-1px);
}

.slot.highlight {
    background: linear-gradient(180deg, #2196f3 0%, #1976d2 50%, #2196f3 100%);
    box-shadow:
        0 0 20px rgba(33, 150, 243, 0.8),
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    border-color: #1565c0;
    animation: slotHighlight 0.3s ease-out;
}

@keyframes slotHighlight {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.slot.filled {
    background: var(--wire-color);
    border-color: rgba(0, 0, 0, 0.4);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        inset 0 -1px 2px rgba(255, 255, 255, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.3);
    animation: slotFill 0.5s ease-out;
}

@keyframes slotFill {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.slot::before {
    content: attr(data-position);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.9rem;
    color: #2c3e50;
    font-weight: bold;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    padding: 3px 6px;
    border-radius: 4px;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Game Controls */
.game-controls {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 30px;
    display: flex;
    justify-content: center;
    gap: 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #6c757d;
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-btn.primary {
    background: #28a745;
}

.control-btn.primary:hover {
    background: #218838;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.modal-content p {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.modal-score {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.modal-score span {
    font-weight: bold;
    color: #2c3e50;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #6c757d;
    color: white;
}

.modal-btn:hover {
    transform: translateY(-2px);
}

.modal-btn.primary {
    background: #007bff;
}

.modal-btn.primary:hover {
    background: #0056b3;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cable-work-area {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .tools-container {
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-left, .header-right {
        justify-content: center;
    }

    .tools-container {
        gap: 20px;
    }

    .tool {
        min-width: 120px;
        padding: 20px;
    }

    .cable-workspace {
        padding: 20px 20px 100px 20px; /* Adjusted for mobile */
    }

    .cable-work-area {
        min-height: 320px; /* Reduced for mobile */
        padding-bottom: 15px;
    }

    .ethernet-cable {
        max-width: 350px;
    }

    .cable-sheath {
        width: 280px;
        height: 30px;
    }

    .cable-sheath.stripped {
        width: 230px;
    }

    .connector-body {
        width: 170px;
        height: 100px;
        transform: none;
    }

    .wire-reference {
        grid-template-columns: 1fr;
    }

    .game-controls {
        flex-wrap: wrap;
        gap: 10px;
        padding: 12px 20px;
        position: fixed;
        bottom: 0;
    }

    .control-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding-bottom: 90px; /* More space for mobile footer */
    }

    .game-main {
        padding: 15px;
        gap: 15px;
    }

    .tools-section, .cable-workspace {
        padding: 15px;
    }

    .tools-container {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .ethernet-cable {
        max-width: 300px;
    }

    .cable-sheath {
        width: 240px;
        height: 25px;
    }

    .cable-sheath.stripped {
        width: 200px;
    }

    .game-controls {
        padding: 10px 15px;
        gap: 8px;
    }

    .control-btn {
        font-size: 0.9rem;
        padding: 10px 15px;
    }
}
