<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crossover Cable Termination Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <button class="back-btn" onclick="window.location.href='../index.html'">← Back</button>
                <h1>Crossover Cable Termination</h1>
            </div>
            <div class="header-right">
                <div class="score-display">
                    <span class="score-label">Score:</span>
                    <span class="score-value" id="score">0</span>
                </div>
                <div class="timer-display">
                    <span class="timer-label">Time:</span>
                    <span class="timer-value" id="timer">00:00</span>
                </div>
            </div>
        </header>

        <!-- Main Game Area -->
        <main class="game-main">
            <!-- Tools Panel -->
            <section class="tools-section">
                <h2>Tools</h2>
                <div class="tools-container">
                    <div class="tool" id="wire-stripper" data-tool="stripper">
                        <div class="tool-icon">🔧</div>
                        <span>Wire Stripper</span>
                        <div class="tool-description">Strip cable sheaths</div>
                    </div>
                    <div class="tool" id="crimping-tool" data-tool="crimper">
                        <div class="tool-icon">🔨</div>
                        <span>Crimping Tool</span>
                        <div class="tool-description">Crimp connectors</div>
                    </div>
                </div>
            </section>

            <!-- Cable Work Area -->
            <section class="cable-workspace">
                <div class="workspace-header">
                    <h2>Crossover Cable Termination Workspace</h2>
                    <div class="current-step" id="current-step">Step 1: Strip both cable ends</div>
                </div>

                <div class="cable-work-area">
                    <!-- End A -->
                    <div class="cable-end" id="cable-end-a">
                        <h3>End A - T568A Standard</h3>
                        <div class="ethernet-cable">
                            <div class="cable-sheath" id="cable-sheath-a" data-end="a"></div>
                            <div class="cable-wires" id="cable-wires-a">
                                <!-- Wires will be dynamically generated -->
                            </div>
                        </div>

                        <div class="rj45-connector" id="rj45-connector-a">
                            <div class="connector-body">
                                <div class="connector-label">T568A</div>
                                <div class="connector-slots">
                                    <div class="slot" data-position="1" data-end="a"></div>
                                    <div class="slot" data-position="2" data-end="a"></div>
                                    <div class="slot" data-position="3" data-end="a"></div>
                                    <div class="slot" data-position="4" data-end="a"></div>
                                    <div class="slot" data-position="5" data-end="a"></div>
                                    <div class="slot" data-position="6" data-end="a"></div>
                                    <div class="slot" data-position="7" data-end="a"></div>
                                    <div class="slot" data-position="8" data-end="a"></div>
                                </div>
                            </div>
                        </div>

                        <!-- T568A Wire Order Reference -->
                        <div class="wire-order-reference">
                            <h4>T568A Wire Order</h4>
                            <div class="wire-reference">
                                <div class="wire-ref" data-color="green-white">1. Green/White</div>
                                <div class="wire-ref" data-color="green">2. Green</div>
                                <div class="wire-ref" data-color="orange-white">3. Orange/White</div>
                                <div class="wire-ref" data-color="blue">4. Blue</div>
                                <div class="wire-ref" data-color="blue-white">5. Blue/White</div>
                                <div class="wire-ref" data-color="orange">6. Orange</div>
                                <div class="wire-ref" data-color="brown-white">7. Brown/White</div>
                                <div class="wire-ref" data-color="brown">8. Brown</div>
                            </div>
                        </div>
                    </div>

                    <!-- Cable Center -->
                    <div class="cable-center">
                        <div class="cable-body"></div>
                        <div class="crossover-indicator">
                            <span>CROSSOVER</span>
                            <div class="crossover-lines">
                                <div class="line line-1"></div>
                                <div class="line line-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- End B -->
                    <div class="cable-end" id="cable-end-b">
                        <h3>End B - T568B Standard</h3>
                        <div class="ethernet-cable">
                            <div class="cable-sheath" id="cable-sheath-b" data-end="b"></div>
                            <div class="cable-wires" id="cable-wires-b">
                                <!-- Wires will be dynamically generated -->
                            </div>
                        </div>

                        <div class="rj45-connector" id="rj45-connector-b">
                            <div class="connector-body">
                                <div class="connector-label">T568B</div>
                                <div class="connector-slots">
                                    <div class="slot" data-position="1" data-end="b"></div>
                                    <div class="slot" data-position="2" data-end="b"></div>
                                    <div class="slot" data-position="3" data-end="b"></div>
                                    <div class="slot" data-position="4" data-end="b"></div>
                                    <div class="slot" data-position="5" data-end="b"></div>
                                    <div class="slot" data-position="6" data-end="b"></div>
                                    <div class="slot" data-position="7" data-end="b"></div>
                                    <div class="slot" data-position="8" data-end="b"></div>
                                </div>
                            </div>
                        </div>

                        <!-- T568B Wire Order Reference -->
                        <div class="wire-order-reference">
                            <h4>T568B Wire Order</h4>
                            <div class="wire-reference">
                                <div class="wire-ref" data-color="orange-white">1. Orange/White</div>
                                <div class="wire-ref" data-color="orange">2. Orange</div>
                                <div class="wire-ref" data-color="green-white">3. Green/White</div>
                                <div class="wire-ref" data-color="blue">4. Blue</div>
                                <div class="wire-ref" data-color="blue-white">5. Blue/White</div>
                                <div class="wire-ref" data-color="green">6. Green</div>
                                <div class="wire-ref" data-color="brown-white">7. Brown/White</div>
                                <div class="wire-ref" data-color="brown">8. Brown</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Game Controls -->
        <footer class="game-controls">
            <button class="control-btn" id="reset-btn">Reset Game</button>
            <button class="control-btn" id="hint-btn">Show Hint</button>
            <button class="control-btn primary" id="check-btn">Check Work</button>
        </footer>
    </div>

    <!-- Game Modals -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <h2>🎉 Outstanding Work!</h2>
            <p>You've successfully terminated the crossover cable with both T568A and T568B standards!</p>
            <div class="modal-score">
                <span>Final Score: <span id="final-score">0</span></span>
                <span>Time: <span id="final-time">00:00</span></span>
            </div>
            <div class="modal-actions">
                <button class="modal-btn" onclick="resetGame()">Play Again</button>
                <button class="modal-btn primary" onclick="window.location.href='../index.html'">Main Menu</button>
            </div>
        </div>
    </div>

    <div class="modal" id="hint-modal">
        <div class="modal-content">
            <h2>💡 Hint</h2>
            <p id="hint-text">Click on the wire stripper to begin stripping both cable ends.</p>
            <div class="modal-actions">
                <button class="modal-btn" onclick="closeModal('hint-modal')">Got it!</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
