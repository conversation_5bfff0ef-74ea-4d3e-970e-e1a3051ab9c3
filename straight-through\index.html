<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Straight-Through Cable Termination Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <button class="back-btn" onclick="window.location.href='../index.html'">← Back</button>
                <h1>Straight-Through Cable Termination</h1>
            </div>
            <div class="header-right">
                <div class="score-display">
                    <span class="score-label">Score:</span>
                    <span class="score-value" id="score">0</span>
                </div>
                <div class="timer-display">
                    <span class="timer-label">Time:</span>
                    <span class="timer-value" id="timer">00:00</span>
                </div>
            </div>
        </header>

        <!-- Main Game Area -->
        <main class="game-main">
            <!-- Tools Panel -->
            <section class="tools-section">
                <h2>Tools</h2>
                <div class="tools-container">
                    <div class="tool" id="wire-stripper" data-tool="stripper">
                        <div class="tool-icon">🔧</div>
                        <span>Wire Stripper</span>
                        <div class="tool-description">Strip cable sheath</div>
                    </div>
                    <div class="tool" id="crimping-tool" data-tool="crimper">
                        <div class="tool-icon">🔨</div>
                        <span>Crimping Tool</span>
                        <div class="tool-description">Crimp connector</div>
                    </div>
                </div>
            </section>

            <!-- Cable Work Area -->
            <section class="cable-workspace">
                <div class="workspace-header">
                    <h2>Cable Termination Workspace</h2>
                    <div class="current-step" id="current-step">Step 1: Strip the cable sheath</div>
                </div>

                <div class="cable-work-area">
                    <!-- Ethernet Cable -->
                    <div class="cable-section">
                        <h3>Ethernet Cable</h3>
                        <div class="ethernet-cable" id="ethernet-cable">
                            <div class="cable-sheath" id="cable-sheath"></div>
                            <div class="cable-wires" id="cable-wires">
                                <!-- Wires will be dynamically generated -->
                            </div>
                        </div>
                    </div>

                    <!-- RJ45 Connector -->
                    <div class="connector-section">
                        <h3>RJ45 Connector (T568B)</h3>
                        <div class="rj45-connector" id="rj45-connector">
                            <div class="connector-body">
                                <div class="connector-slots">
                                    <div class="slot" data-position="1"></div>
                                    <div class="slot" data-position="2"></div>
                                    <div class="slot" data-position="3"></div>
                                    <div class="slot" data-position="4"></div>
                                    <div class="slot" data-position="5"></div>
                                    <div class="slot" data-position="6"></div>
                                    <div class="slot" data-position="7"></div>
                                    <div class="slot" data-position="8"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Wire Order Reference -->
                        <div class="wire-order-reference">
                            <h4>T568B Wire Order</h4>
                            <div class="wire-reference">
                                <div class="wire-ref" data-color="orange-white">1. Orange/White</div>
                                <div class="wire-ref" data-color="orange">2. Orange</div>
                                <div class="wire-ref" data-color="green-white">3. Green/White</div>
                                <div class="wire-ref" data-color="blue">4. Blue</div>
                                <div class="wire-ref" data-color="blue-white">5. Blue/White</div>
                                <div class="wire-ref" data-color="green">6. Green</div>
                                <div class="wire-ref" data-color="brown-white">7. Brown/White</div>
                                <div class="wire-ref" data-color="brown">8. Brown</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Game Controls -->
        <footer class="game-controls">
            <button class="control-btn" id="reset-btn">Reset Game</button>
            <button class="control-btn" id="hint-btn">Show Hint</button>
            <button class="control-btn primary" id="check-btn">Check Work</button>
        </footer>
    </div>

    <!-- Game Modals -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <h2>🎉 Excellent Work!</h2>
            <p>You've successfully terminated the straight-through cable!</p>
            <div class="modal-score">
                <span>Final Score: <span id="final-score">0</span></span>
                <span>Time: <span id="final-time">00:00</span></span>
            </div>
            <div class="modal-actions">
                <button class="modal-btn" onclick="resetGame()">Play Again</button>
                <button class="modal-btn primary" onclick="window.location.href='../index.html'">Main Menu</button>
            </div>
        </div>
    </div>

    <div class="modal" id="hint-modal">
        <div class="modal-content">
            <h2>💡 Hint</h2>
            <p id="hint-text">Click on the wire stripper to begin stripping the cable sheath.</p>
            <div class="modal-actions">
                <button class="modal-btn" onclick="closeModal('hint-modal')">Got it!</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>